<!-- Modern Gradient Background -->
<div class="min-h-screen bg-gradient-to-br from-purple-400 via-purple-500 to-blue-600 relative overflow-hidden">
  <!-- Background decorative elements -->
  <div class="absolute inset-0">
    <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-purple-400/20 via-transparent to-blue-600/20"></div>
    <div class="absolute top-20 right-20 w-64 h-64 bg-white/10 rounded-full blur-3xl"></div>
    <div class="absolute bottom-20 left-20 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>
  </div>

  <div class="relative z-10 container mx-auto px-4 py-16">
    <!-- Hero Section -->
    <section class="text-center text-white mb-20">
      <div class="max-w-4xl mx-auto">
        <h1 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
          <ng-container *ngIf="!authService.userLoggedIn(); else welcomeBack">
            Effortless Task Management<br>
            <span class="text-white/90">for Teams and Individuals</span>
          </ng-container>
          <ng-template #welcomeBack>
            Welcome Back, {{ authService.getCurrentUser()?.username }}!
            <span *ngIf="isAdmin()" class="block text-2xl text-white/80 mt-2">(Administrator)</span>
          </ng-template>
        </h1>

        <p class="text-xl md:text-2xl mb-8 text-white/90 max-w-2xl mx-auto leading-relaxed">
          <ng-container *ngIf="!authService.userLoggedIn(); else userStats">
            Our service caters to both teams and individuals, ensuring everyone can stay organized and focused.
          </ng-container>
          <ng-template #userStats>
            You have <strong>3 active projects</strong> with <strong>5 pending tasks</strong>.
          </ng-template>
        </p>

        <!-- Email Input and CTA -->
        <div class="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12 max-w-md mx-auto">
          <ng-container *ngIf="!authService.userLoggedIn(); else dashboardLink">
            <input
              type="email"
              placeholder="Enter your email"
              class="flex-1 px-6 py-4 rounded-full text-gray-800 placeholder-gray-500 border-0 focus:ring-4 focus:ring-white/30 outline-none text-lg"
            >
            <a
              routerLink="/registeruser"
              class="bg-black text-white px-8 py-4 rounded-full font-semibold hover:bg-gray-800 transition-all duration-300 flex items-center gap-2 whitespace-nowrap">
              Try it free
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </a>
          </ng-container>

          <ng-template #dashboardLink>
            <div class="flex flex-col sm:flex-row gap-4">
              <a *ngIf="isAdmin()"
                 routerLink="/admin/dashboard"
                 class="bg-white text-purple-600 px-8 py-4 rounded-full font-semibold hover:bg-gray-50 transition-all duration-300 shadow-lg">
                Go to Dashboard
              </a>
              <a routerLink="/projects"
                 class="bg-black text-white px-8 py-4 rounded-full font-semibold hover:bg-gray-800 transition-all duration-300">
                View Projects
              </a>
            </div>
          </ng-template>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="mb-20">
      <!-- Dark Feature Card -->
      <div class="bg-black rounded-3xl p-8 md:p-12 mb-16 text-white relative overflow-hidden">
        <div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-purple-500/20 to-transparent rounded-full blur-3xl"></div>
        <div class="relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          <div>
            <h2 class="text-3xl md:text-4xl font-bold mb-6">
              Track real-time<br>progress with Reports
            </h2>
            <p class="text-gray-300 mb-8 text-lg leading-relaxed">
              Gain valuable insights into your productivity and project status with our real-time reporting dashboard.
            </p>
            <a routerLink="/registeruser" class="bg-white text-black px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-all duration-300">
              Get Started
            </a>
          </div>
          <div class="relative">
            <!-- Mock dashboard preview -->
            <div class="bg-white rounded-2xl p-6 shadow-2xl">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-black font-semibold">Performance</h3>
                <span class="text-gray-500 text-sm">16 hr 30 min</span>
              </div>
              <div class="text-black text-2xl font-bold mb-2">54.34%</div>
              <div class="h-32 bg-gradient-to-r from-purple-400 to-blue-500 rounded-lg mb-4 relative">
                <div class="absolute bottom-4 left-4 text-white text-sm">
                  <div class="flex items-center gap-2 mb-1">
                    <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <span>Achieved</span>
                  </div>
                  <div class="font-bold">30</div>
                </div>
              </div>
              <div class="flex justify-between text-gray-500 text-sm">
                <span>20 Aug, 2024</span>
                <span>20 hr 30 min</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Stats Section -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
        <div class="text-center text-white">
          <div class="text-4xl md:text-5xl font-bold mb-2">120K+</div>
          <p class="text-white/80">Thousands trust it to manage, join team and discover the benefits!</p>
        </div>
        <div class="text-center text-white">
          <div class="text-4xl md:text-5xl font-bold mb-2">4.8</div>
          <p class="text-white/80">Positive ratings by users around the world. Check the review here</p>
        </div>
        <div class="text-center text-white">
          <div class="text-4xl md:text-5xl font-bold mb-2">100%</div>
          <p class="text-white/80">User satisfaction with bordup, reflecting improved project</p>
        </div>
      </div>
    </section>

    <!-- Testimonials Section -->
    <section class="mb-20">
      <h2 class="text-3xl md:text-4xl font-bold text-center text-white mb-16">
        What are they say after<br>using DevBridge
      </h2>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Testimonial 1 -->
        <div class="bg-white rounded-2xl p-8 shadow-lg">
          <div class="flex items-center mb-4">
            <div class="flex text-yellow-400 mb-2">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
            </div>
          </div>
          <p class="text-gray-700 mb-6 leading-relaxed">
            "DevBridge has completely transformed how our team manages projects. The intuitive interface and powerful features make collaboration effortless."
          </p>
          <div class="flex items-center">
            <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Sarah Johnson" class="w-12 h-12 rounded-full mr-4">
            <div>
              <h4 class="font-semibold text-gray-900">Sarah Johnson</h4>
              <p class="text-gray-600 text-sm">Project Manager</p>
            </div>
          </div>
        </div>

        <!-- Testimonial 2 -->
        <div class="bg-white rounded-2xl p-8 shadow-lg">
          <div class="flex items-center mb-4">
            <div class="flex text-yellow-400 mb-2">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
            </div>
          </div>
          <p class="text-gray-700 mb-6 leading-relaxed">
            "The real-time analytics and reporting features have given us incredible insights into our productivity. Highly recommended!"
          </p>
          <div class="flex items-center">
            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Mike Chen" class="w-12 h-12 rounded-full mr-4">
            <div>
              <h4 class="font-semibold text-gray-900">Mike Chen</h4>
              <p class="text-gray-600 text-sm">Team Lead</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Final CTA Section -->
    <section class="text-center text-white mb-16">
      <h2 class="text-3xl md:text-4xl font-bold mb-6">
        {{ authService.userLoggedIn() ? 'Ready to boost your productivity?' : 'Ready to boost your productivity?' }}
      </h2>
      <div class="flex flex-col sm:flex-row items-center justify-center gap-4 max-w-md mx-auto">
        <ng-container *ngIf="!authService.userLoggedIn(); else loggedInCta">
          <input
            type="email"
            placeholder="Enter your email"
            class="flex-1 px-6 py-4 rounded-full text-gray-800 placeholder-gray-500 border-0 focus:ring-4 focus:ring-white/30 outline-none text-lg"
          >
          <a
            routerLink="/registeruser"
            class="bg-black text-white px-8 py-4 rounded-full font-semibold hover:bg-gray-800 transition-all duration-300 flex items-center gap-2 whitespace-nowrap">
            Try it free
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </ng-container>

        <ng-template #loggedInCta>
          <div class="flex flex-col sm:flex-row gap-4">
            <a *ngIf="isAdmin()"
               routerLink="/admin/dashboard"
               class="bg-white text-purple-600 px-8 py-4 rounded-full font-semibold hover:bg-gray-50 transition-all duration-300 shadow-lg">
              Go to Dashboard
            </a>
            <a routerLink="/projects"
               class="bg-black text-white px-8 py-4 rounded-full font-semibold hover:bg-gray-800 transition-all duration-300">
              View Projects
            </a>
          </div>
        </ng-template>
      </div>
    </section>
  </div>
</div>