:root {
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* Border radius */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);

  /* Professional shadows (no glow effects) */
  --shadow-focus: 0 0 0 3px rgba(37, 99, 235, 0.1);

  /* Professional 3-color theme */
  --accent-color: #2563eb;
  --secondary-color: #64748b;
  --dark-bg: #0f172a;
  --medium-bg: #1e293b;
  --light-bg: #334155;
  --text-light: #f1f5f9;
  --text-dim: #94a3b8;
}

/* Professional theme variants - all use the same clean 3-color palette */
.theme-feminine,
.theme-masculine,
.theme-neutral {
  --accent-color: #2563eb;
  --secondary-color: #64748b;
  --shadow-focus: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Professional dark mode */
.dark {
  --dark-bg: #0f172a;
  --medium-bg: #1e293b;
  --light-bg: #334155;
  --text-light: #f1f5f9;
  --text-dim: #94a3b8;
  --accent-color: #3b82f6;
  --secondary-color: #64748b;
  --shadow-focus: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Professional light mode */
.light {
  --dark-bg: #f8fafc;
  --medium-bg: #ffffff;
  --light-bg: #ffffff;
  --text-light: #64748b;
  --text-dim: #94a3b8;
  --accent-color: #2563eb;
  --secondary-color: #64748b;
  --shadow-focus: 0 0 0 3px rgba(37, 99, 235, 0.1);
}
