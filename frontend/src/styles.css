/* You can add global styles to this file, and also import other style files */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Angular calendar styles are imported in angular.json */

/* Dark mode styles are now included directly in this file */

/* Import theme variables */
@import "./assets/css/theme-variables.css";

/* Professional clean styles */
@layer base {
  body {
    @apply bg-background text-neutral transition-colors duration-200;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }

  /* Dark mode styles */
  .dark body {
    @apply bg-background-dark text-neutral-100;
  }

  /* Card components */
  .card {
    @apply bg-background-secondary shadow-sm rounded-lg border border-neutral-200 transition-all duration-200;
  }

  .dark .card {
    @apply bg-neutral-800 border-neutral-700 shadow-lg;
  }

  /* Button styles */
  .btn-primary {
    @apply bg-primary hover:bg-primary-dark text-white font-medium px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md;
  }

  .dark .btn-primary {
    @apply bg-primary hover:bg-primary-light;
  }

  /* Input styles */
  input,
  textarea,
  select {
    @apply bg-background-secondary border border-neutral-300 rounded-lg px-3 py-2 text-neutral-700 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-200;
  }

  .dark input,
  .dark textarea,
  .dark select {
    @apply bg-neutral-800 border-neutral-600 text-neutral-100 focus:ring-primary/30 focus:border-primary;
  }

  /* Secondary button styles */
  .btn-secondary {
    @apply bg-neutral-100 hover:bg-neutral-200 text-neutral-700 font-medium px-4 py-2 rounded-lg transition-all duration-200 border border-neutral-300;
  }

  .dark .btn-secondary {
    @apply bg-neutral-700 hover:bg-neutral-600 text-neutral-100 border-neutral-600;
  }
}

/* Professional clean variables */
:root {
  /* Common variables */
  --transition-fast: 0.2s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.5s ease;
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-full: 9999px;

  /* Professional 3-color palette for light mode */
  --bg: #f8fafc;
  --medium-bg: #ffffff;
  --light-bg: #ffffff;
  --accent-color: #2563eb;
  --accent-color-dark: #1d4ed8;
  --accent-color-light: #3b82f6;
  --secondary-color: #64748b;
  --secondary-color-dark: #475569;
  --secondary-color-light: #94a3b8;
  --text-light: #64748b;
  --text-dim: #94a3b8;
  --text-dark: #334155;
  --accent-transparent: rgba(37, 99, 235, 0.1);
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);

  /* Clean gradients */
  --primary-gradient: linear-gradient(
    135deg,
    var(--accent-color),
    var(--accent-color-light)
  );
}

/* Professional dark mode variables */
.dark {
  --bg: #0f172a;
  --medium-bg: #1e293b;
  --light-bg: #334155;
  --accent-color: #3b82f6;
  --accent-color-dark: #2563eb;
  --accent-color-light: #60a5fa;
  --secondary-color: #64748b;
  --secondary-color-dark: #475569;
  --secondary-color-light: #94a3b8;
  --text-light: #f1f5f9;
  --text-dim: #94a3b8;
  --text-dark: #64748b;
  --accent-transparent: rgba(59, 130, 246, 0.1);
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4);
}

/* Styles des champs de saisie futuristes - Mode clair */
.futuristic-input-field {
  background-color: rgba(79, 95, 173, 0.05);
  border: 1px solid rgba(79, 95, 173, 0.2);
  border-radius: var(--border-radius-md);
  color: var(--text-light);
  padding: 0.5rem 1rem;
  transition: all var(--transition-fast);
}

.futuristic-input-field:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: var(--glow-effect);
}

.futuristic-input-field::placeholder {
  color: var(--text-dim);
}

/* Styles des champs de saisie futuristes - Mode sombre */
.dark .futuristic-input-field {
  background-color: rgba(0, 247, 255, 0.05);
  border: 1px solid rgba(0, 247, 255, 0.2);
  border-radius: var(--border-radius-md);
  color: var(--text-light);
  padding: 0.5rem 1rem;
  transition: all var(--transition-fast);
}

.dark .futuristic-input-field:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: var(--glow-effect);
}

.dark .futuristic-input-field::placeholder {
  color: var(--text-dim);
}

/* Styles des boutons d'action futuristes - Mode clair */
.futuristic-action-button {
  background-color: rgba(79, 95, 173, 0.1);
  color: var(--accent-color);
  border: none;
  border-radius: var(--border-radius-full);
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.futuristic-action-button:hover {
  background-color: rgba(79, 95, 173, 0.2);
  transform: translateY(-2px);
  box-shadow: var(--glow-effect);
}

.futuristic-action-button:active {
  transform: translateY(0);
}

/* Styles des boutons d'action futuristes - Mode sombre */
.dark .futuristic-action-button {
  background-color: rgba(0, 247, 255, 0.1);
  color: var(--accent-color);
  border: none;
  border-radius: var(--border-radius-full);
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.dark .futuristic-action-button:hover {
  background-color: rgba(0, 247, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: var(--glow-effect);
}

.dark .futuristic-action-button:active {
  transform: translateY(0);
}
/* Styles de la barre de défilement - Mode clair */
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-track {
  background: var(--medium-bg);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--accent-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-color-light);
  box-shadow: var(--glow-effect);
}

/* Styles de la barre de défilement - Mode sombre */
.dark ::-webkit-scrollbar-track {
  background: var(--medium-bg);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--accent-color);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--accent-color-light);
  box-shadow: var(--glow-effect);
}

/* Animation pour les bulles de message futuristes */
.message-enter {
  opacity: 0;
  transform: translateY(10px);
  filter: blur(5px);
}

.message-enter-active {
  opacity: 1;
  transform: translateY(0);
  filter: blur(0);
  transition: opacity 400ms, transform 400ms, filter 400ms;
}

/* Indicateur de frappe futuriste */
.typing-indicator {
  display: inline-flex;
  align-items: center;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  margin: 0 2px;
  background-color: var(--accent-color);
  border-radius: 50%;
  display: inline-block;
  animation: typing 1.2s infinite ease-in-out;
  box-shadow: 0 0 5px var(--accent-color);
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%,
  100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-5px);
    opacity: 1;
    box-shadow: 0 0 8px var(--accent-color);
  }
}
/* Animation pour les nouvelles notifications futuristes */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 247, 255, 0.7);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 10px 3px rgba(0, 247, 255, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 247, 255, 0);
  }
}

/* Animation pour l'effet de brillance sur les bordures */
@keyframes shine {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shine {
  animation: shine 3s linear infinite;
}

.notification-pulse {
  animation: pulse 1.5s infinite;
  color: var(--accent-color);
}

/* Style pour le badge de notification */
.notification-badge {
  font-weight: bold;
  z-index: 20;
  transform-origin: center;
  min-width: 22px !important;
  min-height: 22px !important;
  padding: 0 5px !important;
  font-size: 0.8rem !important;
  line-height: 1.2 !important;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  animation: notificationPulse 2s infinite;
  position: absolute !important;
  top: -10px !important;
  right: -10px !important;
  border: 2px solid white !important;
  box-shadow: 0 0 0 2px rgba(255, 107, 105, 0.3),
    0 0 10px rgba(255, 107, 105, 0.5) !important;
}

@keyframes notificationPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 105, 0.7);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 10px 3px rgba(255, 107, 105, 0.5);
    transform: scale(1.1);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 105, 0);
    transform: scale(1);
  }
}

/* Style spécifique pour le badge de notification dans la barre latérale */
.relative.z-10.flex.items-center .notification-badge {
  position: relative !important;
  top: 0 !important;
  right: 0 !important;
  margin-left: 8px !important;
  margin-right: -5px !important;
}

/* Transition pour le sidebar */
.sidebar-transition {
  transition: transform 0.3s ease-in-out;
}

/* Style pour les messages non lus futuristes */
.unread-message {
  @apply border-l-4;
  background-color: rgba(0, 247, 255, 0.05);
  border-color: var(--accent-color);
  box-shadow: inset 0 0 10px rgba(0, 247, 255, 0.1);
}

/* Style pour les indicateurs de statut futuristes - Mode clair */
.status-indicator {
  @apply absolute bottom-0 right-0 w-3 h-3 rounded-full border-2;
  border-color: var(--medium-bg);
}

.status-online {
  background-color: #4caf50;
  box-shadow: 0 0 5px rgba(76, 175, 80, 0.6);
}

.status-offline {
  background-color: var(--text-dim);
}

/* Style pour les indicateurs de statut futuristes - Mode sombre */
.dark .status-indicator {
  @apply absolute bottom-0 right-0 w-3 h-3 rounded-full border-2;
  border-color: var(--medium-bg);
}

.dark .status-online {
  background-color: #00ff9d;
  box-shadow: 0 0 5px rgba(0, 255, 157, 0.8);
}

.dark .status-offline {
  background-color: var(--text-dim);
}

/* Styles personnalisés pour Lightbox2 */
.lb-outerContainer {
  background-color: rgba(10, 14, 23, 0.9) !important;
  border-radius: var(--border-radius-lg) !important;
  border: 1px solid rgba(0, 247, 255, 0.3) !important;
  box-shadow: 0 0 30px rgba(0, 247, 255, 0.3) !important;
}

.lb-dataContainer {
  background-color: rgba(10, 14, 23, 0.9) !important;
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg) !important;
  border: 1px solid rgba(0, 247, 255, 0.3) !important;
  border-top: none !important;
  box-shadow: 0 10px 30px rgba(0, 247, 255, 0.3) !important;
}

.lb-image {
  border-radius: var(--border-radius-md) !important;
  border: 2px solid rgba(0, 247, 255, 0.2) !important;
}

.lb-nav a.lb-prev,
.lb-nav a.lb-next {
  opacity: 0.5 !important;
}

.lb-nav a.lb-prev:hover,
.lb-nav a.lb-next:hover {
  opacity: 1 !important;
}

.lb-cancel {
  background-color: var(--accent-color) !important;
  border-radius: 50% !important;
}

.lb-data .lb-caption {
  color: var(--accent-color) !important;
  font-size: 1rem !important;
}

.lb-data .lb-number {
  color: var(--text-dim) !important;
}

.lb-data .lb-close {
  filter: invert(1) hue-rotate(180deg) brightness(1.5) !important;
}

.lb-data .lb-close:hover {
  filter: invert(1) hue-rotate(180deg) brightness(2) !important;
}
