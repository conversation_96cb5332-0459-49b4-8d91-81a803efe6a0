/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{html,ts}"],
  darkMode: "class",
  theme: {
    extend: {
      colors: {
        // Professional 3-color palette
        primary: {
          DEFAULT: "#2563eb", // Professional blue
          light: "#3b82f6",
          dark: "#1d4ed8",
          50: "#eff6ff",
          100: "#dbeafe",
          500: "#2563eb",
          600: "#1d4ed8",
          700: "#1e40af",
        },
        neutral: {
          DEFAULT: "#64748b", // Professional gray
          light: "#94a3b8",
          dark: "#475569",
          50: "#f8fafc",
          100: "#f1f5f9",
          200: "#e2e8f0",
          300: "#cbd5e1",
          400: "#94a3b8",
          500: "#64748b",
          600: "#475569",
          700: "#334155",
          800: "#1e293b",
          900: "#0f172a",
        },
        background: {
          DEFAULT: "#f8fafc", // Clean light background
          secondary: "#ffffff",
          dark: "#0f172a",
        },
        // Simplified semantic colors
        success: "#10b981",
        danger: "#ef4444",
        warning: "#f59e0b",
        info: "#2563eb",
      },
      animation: {
        "pulse-slow": "pulse 8s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        "float-slow": "float 15s ease-in-out infinite",
        "spin-slow": "spin 8s linear infinite",
        "bounce-slow": "bounce 3s infinite",
        glow: "glow 2s ease-in-out infinite alternate",
      },
      keyframes: {
        float: {
          "0%, 100%": { transform: "translateY(0)" },
          "50%": { transform: "translateY(-20px)" },
        },
        glow: {
          "0%": { boxShadow: "0 0 5px rgba(0, 247, 255, 0.5)" },
          "100%": { boxShadow: "0 0 20px rgba(0, 247, 255, 0.8)" },
        },
      },
      gridTemplateRows: {
        12: "repeat(12, minmax(0, 1fr))",
      },
    },
  },
  plugins: [],
};
