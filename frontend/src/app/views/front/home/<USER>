/* Professional Dark Mode Toggle Button */
.theme-toggle {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-toggle:hover {
  transform: translateY(-1px);
}

.theme-toggle:active {
  transform: translateY(0);
}

/* Smooth icon transitions */
.theme-toggle svg {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Professional hover effect */
.theme-toggle:hover svg {
  filter: drop-shadow(0 2px 4px rgba(37, 99, 235, 0.3));
}

/* Dark mode specific styles */
.dark .theme-toggle:hover svg {
  filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3));
}

/* Responsive positioning */
@media (max-width: 768px) {
  .theme-toggle {
    top: 1rem;
    right: 1rem;
    width: 2.5rem;
    height: 2.5rem;
  }

  .theme-toggle svg {
    width: 1.25rem;
    height: 1.25rem;
  }
}